# WordPress Mega Menü Sistemi Kullanım Kılavuzu

## Genel Bakış

Bu gelişmiş mega menü sistemi, WordPress temalarında çok seviyeli ve dinamik mega menüler oluşturmanıza olanak tanır. Sistem üç farklı mega menü türünü destekler:

1. **Blok Seçili Mega Menü** - Sadece seçilmiş WordPress sayfa/blok içeriği gösterilir
2. **Tam Genişlik Mega Menü** - Alt menüler tam genişlikte sıralanır
3. **İki Bölümlü Mega Menü** - Sol tarafta navigasyon, sağ tarafta dinamik içerik

## Kurulum ve Yapılandırma

### 1. Menü Öğesi Ayarları

WordPress Admin Panel → Görünüm → Menüler bölümünde:

1. Menü öğesini seçin
2. "CSS Sınıfları" alanına `has-mega-menu` ekleyin
3. <PERSON>steğ<PERSON> bağlı olarak bir WordPress sayfa/blok seçin

### 2. Mega Menü Türleri

#### A) Blok Seçili Mega Menü
- Menü öğesine `has-mega-menu` sınıfı ekleyin
- Bir WordPress sayfa/blok seçin
- Alt menü varlığı bu durumu etkilemez
- Sadece seçilmiş içerik mega menüde gösterilir

#### B) Tam Genişlik Mega Menü
- Menü öğesine `has-mega-menu` sınıfı ekleyin
- Sayfa/blok seçmeyin
- Alt menüler varsa tam genişlikte gösterilir
- Alt menülerde `has-mega-menu` sınıfı YOKSA bu mod aktif olur

#### C) İki Bölümlü Mega Menü
- Ana menü öğesine `has-mega-menu` sınıfı ekleyin
- Sayfa/blok seçmeyin
- Alt menülerden en az birine `has-mega-menu` sınıfı ekleyin
- O alt menü için bir sayfa/blok seçin
- Sol tarafta alt menü listesi, sağ tarafta dinamik içerik gösterilir

## Teknik Özellikler

### CSS Sınıfları

- `.has-mega-menu` - Mega menü aktif eden ana sınıf
- `.mega-menu-style` - Mega menü container sınıfı
- `.mega-menu-page-only` - Sadece sayfa içerikli mega menü
- `.mega-menu-full` - Tam genişlik mega menü
- `.mega-menu-split` - İki bölümlü mega menü
- `.mega-menu-active` - Aktif mega menü durumu

### JavaScript Özellikleri

- Hover tabanlı mega menü açma/kapama
- Dinamik içerik yükleme (AJAX)
- İçerik önbellekleme
- Responsive davranış
- Animasyonlu geçişler

### Responsive Tasarım

- **Desktop (1024px+)**: Tam mega menü deneyimi
- **Tablet (768px-1024px)**: Basitleştirilmiş layout
- **Mobil (768px-)**: Normal dropdown menü davranışı

## Performans Optimizasyonları

1. **İçerik Önbellekleme**: Sayfa içerikleri JavaScript'te önbelleklenir
2. **AJAX Yükleme**: İçerikler ihtiyaç duyulduğunda yüklenir
3. **Responsive Kontrol**: Cihaz türüne göre davranış değişir
4. **Animasyon Optimizasyonu**: CSS transform ve opacity kullanımı

## Güvenlik

- WordPress nonce kontrolü
- AJAX istekleri güvenli endpoint üzerinden
- Kullanıcı yetki kontrolü
- XSS koruması

## Örnekler

### Örnek 1: E-ticaret Kategorileri
```
Mağaza (has-mega-menu, sayfa seçilmemiş)
├── Kadın (has-mega-menu, "Kadın Ürünleri" sayfası seçili)
├── Erkek (has-mega-menu, "Erkek Ürünleri" sayfası seçili)
└── Çocuk (normal alt menü)
```
Sonuç: Sol tarafta kategori listesi, sağ tarafta hover edilen kategorinin içeriği

### Örnek 2: Kurumsal Sayfa
```
Hakkımızda (has-mega-menu, "Şirket Bilgileri" sayfası seçili)
├── Tarihçe
├── Misyon
└── Vizyon
```
Sonuç: Sadece "Şirket Bilgileri" sayfası içeriği gösterilir

### Örnek 3: Hizmetler
```
Hizmetler (has-mega-menu, sayfa seçilmemiş)
├── Web Tasarım
├── SEO
└── Sosyal Medya
```
Sonuç: Alt menüler tam genişlikte sıralanır

## Sorun Giderme

1. **Mega menü görünmüyor**: CSS sınıfının doğru eklendiğini kontrol edin
2. **İçerik yüklenmiyor**: AJAX endpoint'inin çalıştığını kontrol edin
3. **Responsive sorunlar**: Tarayıcı geliştirici araçlarında CSS'i kontrol edin
4. **Performans sorunları**: İçerik önbellekleme aktif mi kontrol edin

## Destek

Bu mega menü sistemi DmrThema için özel olarak geliştirilmiştir. Teknik destek için tema geliştiricisi ile iletişime geçin.
