/**
 * Gelismis Mega Menu JavaScript for DmrThema
 * Cok seviyeli mega menu sistemi ve dinamik icerik gosterimi
 */

document.addEventListener('DOMContentLoaded', function() {

    // Mega menu elementlerini sec
    const megaMenuItems = document.querySelectorAll('.main-navigation ul li.has-mega-menu');

    if (megaMenuItems.length === 0) {
        return; // Mega menu yoksa cik
    }

    // Her mega menu item icin event listener'lar ekle
    megaMenuItems.forEach(function(menuItem) {
        const subMenu = menuItem.querySelector('.sub-menu');

        if (!subMenu) {
            return; // Alt menu yoksa devam et
        }

        let hoverTimeout;

        // Menu item uzerine fare geldiginde
        menuItem.addEventListener('mouseenter', function() {
            // Timeout'u temizle (eger varsa)
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            // Diger acik mega menuleri kapat
            closeMegaMenus();

            // Bu mega menuyu ac - animasyon icin kisa gecikme
            requestAnimationFrame(function() {
                menuItem.classList.add('mega-menu-active');

                // Eger split layout ise, ilk alt menu itemini aktif yap
                initializeSplitMegaMenu(subMenu);
            });
        });

        // Menu item'dan fare ciktiginda
        menuItem.addEventListener('mouseleave', function() {
            // Kisa bir gecikme ile menu kapat
            hoverTimeout = setTimeout(function() {
                menuItem.classList.remove('mega-menu-active');
                clearSplitMegaMenu(subMenu);
            }, 150); // 150ms gecikme
        });

        // Sub menu uzerine fare geldiginde
        subMenu.addEventListener('mouseenter', function() {
            // Timeout'u temizle - menu acik kalsin
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            // Menu acik kalsin
            menuItem.classList.add('mega-menu-active');
        });

        // Sub menu'dan fare ciktiginda
        subMenu.addEventListener('mouseleave', function() {
            // Menu kapat
            hoverTimeout = setTimeout(function() {
                menuItem.classList.remove('mega-menu-active');
                clearSplitMegaMenu(subMenu);
            }, 150);
        });

        // Split mega menu icin alt menu hover yonetimi
        setupSplitMegaMenuHover(subMenu);
    });

    // Split mega menu icin alt menu hover yonetimi
    function setupSplitMegaMenuHover(subMenu) {
        if (!subMenu.classList.contains('mega-menu-split')) {
            return;
        }

        const navItems = subMenu.querySelectorAll('.mega-menu-nav > li');
        const contentArea = subMenu.querySelector('.mega-menu-content');

        if (!navItems.length || !contentArea) {
            return;
        }

        navItems.forEach(function(navItem) {
            const link = navItem.querySelector('a');
            const pageId = link ? link.getAttribute('data-page-id') : null;

            navItem.addEventListener('mouseenter', function() {
                // Diger aktif itemlari temizle
                navItems.forEach(function(item) {
                    item.classList.remove('mega-menu-active');
                });

                // Bu itemi aktif yap
                navItem.classList.add('mega-menu-active');

                // Icerik yukle
                if (pageId) {
                    loadMegaMenuContent(contentArea, pageId);
                }
            });
        });
    }

    // Split mega menu baslangic durumu
    function initializeSplitMegaMenu(subMenu) {
        if (!subMenu.classList.contains('mega-menu-split')) {
            return;
        }

        const firstNavItem = subMenu.querySelector('.mega-menu-nav > li');
        const contentArea = subMenu.querySelector('.mega-menu-content');

        if (firstNavItem && contentArea) {
            firstNavItem.classList.add('mega-menu-active');

            const firstLink = firstNavItem.querySelector('a');
            const pageId = firstLink ? firstLink.getAttribute('data-page-id') : null;

            if (pageId) {
                loadMegaMenuContent(contentArea, pageId);
            }
        }
    }

    // Split mega menu temizle
    function clearSplitMegaMenu(subMenu) {
        if (!subMenu.classList.contains('mega-menu-split')) {
            return;
        }

        const navItems = subMenu.querySelectorAll('.mega-menu-nav > li');
        const contentArea = subMenu.querySelector('.mega-menu-content');

        navItems.forEach(function(item) {
            item.classList.remove('mega-menu-active');
        });

        if (contentArea) {
            contentArea.classList.remove('active');
            contentArea.innerHTML = '';
        }
    }

    // Mega menu icerik yukle (AJAX)
    function loadMegaMenuContent(contentArea, pageId) {
        if (!pageId || !contentArea) {
            return;
        }

        // Eger icerik cache'de varsa direkt goster
        if (contentCache[pageId]) {
            contentArea.innerHTML = '<div class="mega-menu-content-item active">' + contentCache[pageId] + '</div>';
            contentArea.classList.add('active');
            return;
        }

        // Loading durumu
        contentArea.innerHTML = '<div class="mega-menu-loading">Yukleniyor...</div>';
        contentArea.classList.add('active');

        // WordPress AJAX kullan
        const formData = new FormData();
        formData.append('action', 'get_mega_menu_content');
        formData.append('page_id', pageId);
        formData.append('nonce', dmrthema_mega_menu.nonce);

        fetch(dmrthema_mega_menu.ajax_url, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.content) {
                const content = data.data.content;
                contentCache[pageId] = content; // Cache'e kaydet
                contentArea.innerHTML = '<div class="mega-menu-content-item active">' + content + '</div>';
            } else {
                contentArea.innerHTML = '<div class="mega-menu-content-item active"><p>Icerik bulunamadi.</p></div>';
            }
        })
        .catch(error => {
            console.error('Mega menu icerik yuklenirken hata:', error);
            contentArea.innerHTML = '<div class="mega-menu-content-item active"><p>Icerik yuklenirken hata olustu.</p></div>';
        });
    }

    // Icerik cache'i
    const contentCache = {};

    // Tum mega menuleri kapat
    function closeMegaMenus() {
        megaMenuItems.forEach(function(item) {
            item.classList.remove('mega-menu-active');
            const subMenu = item.querySelector('.sub-menu');
            if (subMenu) {
                clearSplitMegaMenu(subMenu);
            }
        });
    }

    // Sayfa uzerinde baska bir yere tiklandiginda mega menuleri kapat
    document.addEventListener('click', function(e) {
        // Tiklanilan element mega menu icinde degilse
        let isInsideMegaMenu = false;

        megaMenuItems.forEach(function(menuItem) {
            if (menuItem.contains(e.target)) {
                isInsideMegaMenu = true;
            }
        });

        if (!isInsideMegaMenu) {
            closeMegaMenus();
        }
    });

    // ESC tusuna basildiginda mega menuleri kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMegaMenus();
        }
    });

    // Responsive kontrol - mobilde mega menu davranisini degistir
    function handleResponsiveMegaMenu() {
        const isMobile = window.innerWidth <= 768;

        megaMenuItems.forEach(function(menuItem) {
            const subMenu = menuItem.querySelector('.sub-menu');
            if (subMenu) {
                if (isMobile) {
                    // Mobilde basit dropdown davranisi
                    subMenu.classList.add('mobile-dropdown');
                } else {
                    // Desktop'ta normal mega menu davranisi
                    subMenu.classList.remove('mobile-dropdown');
                }
            }
        });
    }

    // Sayfa yuklendiginde ve pencere boyutu degistiginde responsive kontrol
    handleResponsiveMegaMenu();
    window.addEventListener('resize', handleResponsiveMegaMenu);

});
